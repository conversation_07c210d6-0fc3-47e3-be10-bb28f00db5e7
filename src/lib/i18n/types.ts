// Internationalization types and interfaces

export type SupportedLanguage = 'en' | 'es' | 'to' // Add more languages here as needed

export interface LanguageInfo {
  code: SupportedLanguage
  name: string
  nativeName: string
  flag: string
}

export interface TranslationValues {
  [key: string]: string | number | boolean | Date
}

export interface I18nContextType {
  language: SupportedLanguage
  setLanguage: (language: SupportedLanguage) => void
  t: (key: string, values?: TranslationValues) => string
  isLoading: boolean
  availableLanguages: LanguageInfo[]
}

// Translation key structure - this will be auto-generated from translation files
export interface TranslationKeys {
  // Common
  common: {
    loading: string
    error: string
    success: string
    cancel: string
    save: string
    delete: string
    edit: string
    create: string
    update: string
    confirm: string
    back: string
    next: string
    previous: string
    close: string
    search: string
    clear: string
    refresh: string
    share: string
    copy: string
    paste: string
    add: string
    remove: string
    play: string
    pause: string
    stop: string
    repeat: string
    volume: string
    settings: string
    profile: string
    account: string
    signIn: string
    signOut: string
    signUp: string
    language: string
  }

  // Navigation
  navigation: {
    createQueue: string
    magicQueue: string
    myQueues: string
    publicQueues: string
    settings: string
  }

  // Search
  search: {
    title: string
    description: string
    placeholder: string
    noResults: string
    noResultsDescription: string
    tryManualInput: string
    addVideos: string
    editQueueAddVideos: string
    findAndAddVideos: string
    searchMode: string
    manualMode: string
    pasteYouTubeLink: string
  }

  // Queue
  queue: {
    currentQueue: string
    emptyQueue: string
    emptyQueueDescription: string
    addVideosToStart: string
    queueLoops: string
    infinite: string
    clearQueue: string
    shareQueue: string
    loadQueue: string
    saveQueue: string
    deleteQueue: string
    editQueue: string
    queueTitle: string
    queueDescription: string
    videoCount: string
    totalDuration: string
    createdAt: string
    updatedAt: string
    privacy: string
    private: string
    public: string
  }

  // Magic Queue
  magic: {
    title: string
    description: string
    editTitle: string
    editDescription: string
    placeholder: string
    generating: string
    examples: {
      educational: string
      teachMe: string
      relaxing: string
      guitar: string
      history: string
      cooking: string
      space: string
      language: string
    }
  }

  // Settings
  settings: {
    title: string
    description: string
    profileInformation: string
    displayName: string
    emailAddress: string
    emailCannotChange: string
    accountCreated: string
    lastSignIn: string
    accountType: string
    emailAccount: string
    googleAccount: string
    updateProfile: string
    updating: string
    profileUpdated: string
    updateFailed: string
    signInRequired: string
    signInRequiredDescription: string
    languageSettings: string
    selectLanguage: string
    languageChanged: string
  }

  // Authentication
  auth: {
    signIn: string
    signUp: string
    signOut: string
    email: string
    password: string
    displayName: string
    confirmPassword: string
    forgotPassword: string
    resetPassword: string
    createAccount: string
    alreadyHaveAccount: string
    dontHaveAccount: string
    signInWithGoogle: string
    continueAsGuest: string
    emailRequired: string
    passwordRequired: string
    passwordTooShort: string
    passwordsDoNotMatch: string
    invalidEmail: string
    userNotFound: string
    wrongPassword: string
    emailAlreadyInUse: string
    weakPassword: string
    tooManyRequests: string
    networkError: string
    unknownError: string
  }

  // Video Player
  player: {
    loading: string
    error: string
    noVideo: string
    selectVideo: string
    currentlyPlaying: string
    nextUp: string
    timeframe: string
    startTime: string
    endTime: string
    loopCount: string
    editTimeframe: string
    addTimeframe: string
    removeTimeframe: string
  }

  // Errors
  errors: {
    generic: string
    networkError: string
    notFound: string
    unauthorized: string
    forbidden: string
    serverError: string
    validationError: string
    timeoutError: string
  }

  // Time formats
  time: {
    justNow: string
    minuteAgo: string
    minutesAgo: string
    hourAgo: string
    hoursAgo: string
    dayAgo: string
    daysAgo: string
    weekAgo: string
    weeksAgo: string
    monthAgo: string
    monthsAgo: string
    yearAgo: string
    yearsAgo: string
  }
}

// Supported languages configuration
export const SUPPORTED_LANGUAGES: LanguageInfo[] = [
  {
    code: 'en',
    name: 'English',
    nativeName: 'English',
    flag: '🇺🇸'
  },
  {
    code: 'es',
    name: 'Spanish',
    nativeName: 'Español',
    flag: '🇪🇸'
  },
  {
    code: 'to',
    name: 'Tongan',
    nativeName: 'lea fakatonga',
    flag: '🇹🇴'
  }
  // Add more languages here as needed:
  // {
  //   code: 'fr',
  //   name: 'French',
  //   nativeName: 'Français',
  //   flag: '🇫🇷'
  // }
]

export const DEFAULT_LANGUAGE: SupportedLanguage = 'en'
export const LANGUAGE_STORAGE_KEY = 'youtube-looper-language'
